#include "tile_manager.h"
#include "command_processor.h"
#include "gnm_state.h"
#include <algorithm>
#include <chrono>
#include <cmath>
#include <cstring>
#include <shared_mutex>
#include <spdlog/spdlog.h>
#include <stdexcept>
#include <vector>

namespace ps4 {

void TileInfo::Serialize(std::ostream &out) const {
  out.write(reinterpret_cast<const char *>(&width), sizeof(width));
  out.write(reinterpret_cast<const char *>(&height), sizeof(height));
  out.write(reinterpret_cast<const char *>(&depth), sizeof(depth));
  out.write(reinterpret_cast<const char *>(&mode), sizeof(mode));
  out.write(reinterpret_cast<const char *>(&format), sizeof(format));
  out.write(reinterpret_cast<const char *>(&bytesPerPixel),
            sizeof(bytesPerPixel));
  out.write(reinterpret_cast<const char *>(&pitch), sizeof(pitch));
  out.write(reinterpret_cast<const char *>(&slice), sizeof(slice));
  out.write(reinterpret_cast<const char *>(&gpuAddr), sizeof(gpuAddr));
  out.write(reinterpret_cast<const char *>(&cpuAddr), sizeof(cpuAddr));
  out.write(reinterpret_cast<const char *>(&isRenderTarget),
            sizeof(isRenderTarget));
  out.write(reinterpret_cast<const char *>(&isDepthStencil),
            sizeof(isDepthStencil));
  out.write(reinterpret_cast<const char *>(&cacheHits), sizeof(cacheHits));
  out.write(reinterpret_cast<const char *>(&cacheMisses), sizeof(cacheMisses));
}

void TileInfo::Deserialize(std::istream &in) {
  in.read(reinterpret_cast<char *>(&width), sizeof(width));
  in.read(reinterpret_cast<char *>(&height), sizeof(height));
  in.read(reinterpret_cast<char *>(&depth), sizeof(depth));
  in.read(reinterpret_cast<char *>(&mode), sizeof(mode));
  in.read(reinterpret_cast<char *>(&format), sizeof(format));
  in.read(reinterpret_cast<char *>(&bytesPerPixel), sizeof(bytesPerPixel));
  in.read(reinterpret_cast<char *>(&pitch), sizeof(pitch));
  in.read(reinterpret_cast<char *>(&slice), sizeof(slice));
  in.read(reinterpret_cast<char *>(&gpuAddr), sizeof(gpuAddr));
  in.read(reinterpret_cast<char *>(&cpuAddr), sizeof(cpuAddr));
  in.read(reinterpret_cast<char *>(&isRenderTarget), sizeof(isRenderTarget));
  in.read(reinterpret_cast<char *>(&isDepthStencil), sizeof(isDepthStencil));
  in.read(reinterpret_cast<char *>(&cacheHits), sizeof(cacheHits));
  in.read(reinterpret_cast<char *>(&cacheMisses), sizeof(cacheMisses));
  if (!in.good()) {
    throw TileManagerException("Failed to deserialize TileInfo");
  }
}

void TileManager::TiledSurface::Serialize(std::ostream &out) const {
  info.Serialize(out);
  uint64_t dataSize = data.size();
  out.write(reinterpret_cast<const char *>(&dataSize), sizeof(dataSize));
  out.write(reinterpret_cast<const char *>(data.data()), dataSize);
  out.write(reinterpret_cast<const char *>(&active), sizeof(active));
  out.write(reinterpret_cast<const char *>(&cacheHits), sizeof(cacheHits));
  out.write(reinterpret_cast<const char *>(&cacheMisses), sizeof(cacheMisses));
}

void TileManager::TiledSurface::Deserialize(std::istream &in) {
  info.Deserialize(in);
  uint64_t dataSize;
  in.read(reinterpret_cast<char *>(&dataSize), sizeof(dataSize));
  if (!in.good() ||
      dataSize > 1024 * 1024 * 1024) { // Sanity check: max 1GB per surface
    throw TileManagerException(
        "Invalid surface data size during deserialization");
  }

  data.resize(dataSize);
  if (dataSize > 0) {
    in.read(reinterpret_cast<char *>(data.data()), dataSize);
    if (!in.good()) {
      throw TileManagerException(
          "Failed to read surface data during deserialization");
    }
  }

  in.read(reinterpret_cast<char *>(&active), sizeof(active));
  in.read(reinterpret_cast<char *>(&cacheHits), sizeof(cacheHits));
  in.read(reinterpret_cast<char *>(&cacheMisses), sizeof(cacheMisses));
  if (!in.good()) {
    throw TileManagerException("Failed to deserialize TiledSurface");
  }
}

TileManager::~TileManager() {
  Shutdown();
  spdlog::info("TileManager destroyed");
}

bool TileManager::Initialize() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    m_surfaces.clear();
    m_surfaceCache.clear();
    m_nextSurfaceId = 1;
    m_currentSurfaceId = 0;
    m_currentTileX = 0;
    m_currentTileY = 0;
    m_tilesX = 0;
    m_tilesY = 0;
    m_stats = TileManagerStats();
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    m_stats.cacheHits++;
    spdlog::info("TileManager initialized, latency={}us", latency);
    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("TileManager initialization failed: {}", e.what());
    throw TileManagerException("Initialization failed: " +
                               std::string(e.what()));
  }
}

void TileManager::Shutdown() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    m_surfaces.clear();
    m_surfaceCache.clear();
    m_nextSurfaceId = 1;
    m_currentSurfaceId = 0;
    m_currentTileX = 0;
    m_currentTileY = 0;
    m_tilesX = 0;
    m_tilesY = 0;
    m_stats = TileManagerStats();
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    m_stats.cacheHits++;
    spdlog::info("TileManager shutdown, latency={}us", latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("TileManager shutdown failed: {}", e.what());
  }
}

uint32_t TileManager::GetBytesPerPixelInternal(TileFormat format) {
  // Internal helper method without locking - used when mutex is already held
  switch (format) {
  case TileFormat::R8_UNORM:
    return 1;
  case TileFormat::R8G8_UNORM:
  case TileFormat::D16_UNORM:
  case TileFormat::R16_FLOAT:
    return 2;
  case TileFormat::R8G8B8A8_UNORM:
  case TileFormat::B8G8R8A8_UNORM:
  case TileFormat::R16G16_FLOAT:
  case TileFormat::D24_UNORM_S8_UINT:
  case TileFormat::D32_FLOAT:
  case TileFormat::R32_FLOAT:
    return 4;
  case TileFormat::R16G16B16A16_FLOAT:
  case TileFormat::R32G32_FLOAT:
    return 8;
  case TileFormat::R32G32B32A32_FLOAT:
    return 16;
  default:
    spdlog::error("GetBytesPerPixelInternal: Invalid format {}",
                  static_cast<int>(format));
    throw TileManagerException("Invalid tile format");
  }
}

uint32_t TileManager::GetBytesPerPixel(TileFormat format) {
  auto start = std::chrono::high_resolution_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    uint32_t bytesPerPixel = GetBytesPerPixelInternal(format);
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetBytesPerPixel: format={}, bytes={}, latency={}us",
                  static_cast<int>(format), bytesPerPixel, latency);
    return bytesPerPixel;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetBytesPerPixel failed: {}", e.what());
    throw TileManagerException("GetBytesPerPixel failed: " +
                               std::string(e.what()));
  }
}

void TileManager::CalculateTileLayout(TileInfo &info) {
  // No lock here, assumes caller holds it.
  info.bytesPerPixel = GetBytesPerPixelInternal(info.format);
  uint32_t width_in_tiles = (info.width + TILE_WIDTH - 1) / TILE_WIDTH;
  uint32_t height_in_tiles = (info.height + TILE_HEIGHT - 1) / TILE_HEIGHT;

  switch (info.mode) {
  case TileMode::LINEAR:
    info.pitch = info.width * info.bytesPerPixel;
    info.slice = info.pitch * info.height;
    break;
  case TileMode::TILED_1D:
    info.pitch = width_in_tiles * TILE_WIDTH * info.bytesPerPixel;
    info.slice = info.pitch * info.height;
    break;
  case TileMode::TILED_2D:
  case TileMode::TILED_2B:
  case TileMode::TILED_3D:
  case TileMode::TILED_DEPTH:
    info.pitch = width_in_tiles * TILE_WIDTH * info.bytesPerPixel;
    info.slice = info.pitch * height_in_tiles * TILE_HEIGHT;
    break;
  default:
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("CalculateTileLayout: Unsupported tile mode {}",
                  static_cast<int>(info.mode));
    throw TileManagerException("Unsupported tile mode");
  }
}

uint64_t TileManager::CreateTiledSurface(uint32_t width, uint32_t height,
                                         uint32_t depth, TileMode mode,
                                         TileFormat format, bool isRenderTarget,
                                         bool isDepthStencil) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    if (width == 0 || height == 0 || depth == 0) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("CreateTiledSurface: Invalid dimensions: {}x{}x{}", width,
                    height, depth);
      throw TileManagerException("Invalid surface dimensions");
    }
    if (format == TileFormat::INVALID) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("CreateTiledSurface: Invalid format");
      throw TileManagerException("Invalid surface format");
    }

    uint64_t surfaceId = m_nextSurfaceId++;
    TiledSurface &surface = m_surfaces[surfaceId];
    surface.active = true;
    surface.info.width = width;
    surface.info.height = height;
    surface.info.depth = depth;
    surface.info.mode = mode;
    surface.info.format = format;
    surface.info.isRenderTarget = isRenderTarget;
    surface.info.isDepthStencil = isDepthStencil;
    surface.info.gpuAddr = 0x80000000ULL | (surfaceId << 32);
    surface.info.cpuAddr = 0x40000000ULL | (surfaceId << 32);

    CalculateTileLayout(surface.info);
    size_t totalSize = surface.info.slice * depth;
    surface.data.resize(totalSize, 0);

    if (isRenderTarget) {
      m_currentSurfaceId = surfaceId;
      m_currentTileX = 0;
      m_currentTileY = 0;
      m_tilesX = (width + TILE_WIDTH - 1) / TILE_WIDTH;
      m_tilesY = (height + TILE_HEIGHT - 1) / TILE_HEIGHT;
    }

    // Cache surface data
    SurfaceCacheEntry cacheEntry{surfaceId, surface.data};
    m_surfaceCache[surfaceId] = cacheEntry;
    surface.info.cacheHits++;
    surface.cacheHits++;
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("CreateTiledSurface: Created surface {}: {}x{}x{}, mode={}, "
                 "format={}, size={} bytes, latency={}us",
                 surfaceId, width, height, depth, static_cast<int>(mode),
                 static_cast<int>(format), totalSize, latency);

    lock.unlock();
    m_commandProcessor.NotifySurfaceCreated(surfaceId, width, height,
                                            static_cast<uint32_t>(format));
    lock.lock();

    return surfaceId;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("CreateTiledSurface failed: {}", e.what());
    throw TileManagerException("CreateTiledSurface failed: " +
                               std::string(e.what()));
  }
}

void TileManager::DestroyTiledSurface(uint64_t surfaceId) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    auto it = m_surfaces.find(surfaceId);
    if (it == m_surfaces.end() || !it->second.active) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("DestroyTiledSurface: Invalid surface ID: {}", surfaceId);
      throw TileManagerException("Invalid surface ID");
    }

    it->second.active = false;
    it->second.data.clear();
    it->second.info.cacheHits++;
    it->second.cacheHits++;
    m_surfaceCache.erase(surfaceId);
    if (m_currentSurfaceId == surfaceId) {
      m_currentSurfaceId = 0;
      m_currentTileX = 0;
      m_currentTileY = 0;
      m_tilesX = 0;
      m_tilesY = 0;
    }

    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("DestroyTiledSurface: Destroyed surface {}, latency={}us",
                 surfaceId, latency);

    lock.unlock();
    m_commandProcessor.NotifySurfaceDestroyed(surfaceId);
    lock.lock();
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("DestroyTiledSurface failed: {}", e.what());
    throw TileManagerException("DestroyTiledSurface failed: " +
                               std::string(e.what()));
  }
}

const TileInfo *TileManager::GetTileInfo(uint64_t surfaceId) const {
  auto start = std::chrono::high_resolution_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    auto it = m_surfaces.find(surfaceId);
    if (it == m_surfaces.end() || !it->second.active) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("GetTileInfo: Invalid surface ID: {}", surfaceId);
      return nullptr;
    }

    it->second.info.cacheHits++;
    it->second.cacheHits++;
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetTileInfo: Retrieved info for surface {}, latency={}us",
                  surfaceId, latency);
    return &it->second.info;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetTileInfo failed: {}", e.what());
    return nullptr;
  }
}

void TileManager::LinearToTiled(uint64_t surfaceId, const void *linearData,
                                size_t linearSize) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    auto it = m_surfaces.find(surfaceId);
    if (it == m_surfaces.end() || !it->second.active) {
      throw TileManagerException("Invalid surface ID");
    }

    TileInfo &info = it->second.info;
    void *tiledData = it->second.data.data();

    lock.unlock(); // Release lock during computation
    switch (info.mode) {
    case TileMode::LINEAR:
      std::memcpy(tiledData, linearData,
                  std::min(linearSize, it->second.data.size()));
      break;
    case TileMode::TILED_1D:
      LinearToTiled1D(info, linearData, tiledData);
      break;
    case TileMode::TILED_2D:
      LinearToTiled2D(info, linearData, tiledData);
      break;
    case TileMode::TILED_2B:
      LinearToTiled2B(info, linearData, tiledData);
      break;
    case TileMode::TILED_3D:
      LinearToTiled3D(info, linearData, tiledData);
      break;
    case TileMode::TILED_DEPTH:
      LinearToTiledDepth(info, linearData, tiledData);
      break;
    default:
      throw TileManagerException("Unsupported tile mode");
    }
    lock.lock(); // Re-acquire lock

    m_commandProcessor.NotifySurfaceUpdated(surfaceId, 0, 0, info.width,
                                            info.height);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("LinearToTiled failed: {}", e.what());
    throw TileManagerException("LinearToTiled failed: " +
                               std::string(e.what()));
  }
}

void TileManager::TiledToLinear(uint64_t surfaceId, void *linearData,
                                size_t linearSize) const {
  auto start = std::chrono::high_resolution_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    auto it = m_surfaces.find(surfaceId);
    if (it == m_surfaces.end() || !it->second.active) {
      throw TileManagerException("Invalid surface ID");
    }

    const TileInfo &info = it->second.info;
    const void *tiledData = it->second.data.data();

    lock.unlock(); // Release lock during computation
    switch (info.mode) {
    case TileMode::LINEAR:
      std::memcpy(linearData, tiledData,
                  std::min(linearSize, it->second.data.size()));
      break;
    case TileMode::TILED_1D:
      TiledToLinear1D(info, tiledData, linearData);
      break;
    case TileMode::TILED_2D:
      TiledToLinear2D(info, tiledData, linearData);
      break;
    case TileMode::TILED_2B:
      TiledToLinear2B(info, tiledData, linearData);
      break;
    case TileMode::TILED_3D:
      TiledToLinear3D(info, tiledData, linearData);
      break;
    case TileMode::TILED_DEPTH:
      TiledToLinearDepth(info, tiledData, linearData);
      break;
    default:
      throw TileManagerException("Unsupported tile mode");
    }
    lock.lock(); // Re-acquire lock
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("TiledToLinear failed: {}", e.what());
    throw TileManagerException("TiledToLinear failed: " +
                               std::string(e.what()));
  }
}

uint64_t TileManager::GetGcnTiledAddress(const TileInfo &info, uint32_t x,
                                         uint32_t y, uint32_t z) const {
  // This function provides a simplified model of GCN tiling for emulation.
  // Real hardware tiling is significantly more complex.
  uint32_t bpp = info.bytesPerPixel;
  if (bpp == 0)
    return 0;

  uint32_t pitch_in_tiles = (info.width + TILE_WIDTH - 1) / TILE_WIDTH;

  uint32_t tile_x = x / TILE_WIDTH;
  uint32_t tile_y = y / TILE_HEIGHT;
  uint32_t in_tile_x = x % TILE_WIDTH;
  uint32_t in_tile_y = y % TILE_HEIGHT;

  uint64_t slice_offset = (uint64_t)z * info.slice;
  uint64_t tile_offset_in_slice =
      (tile_y * pitch_in_tiles + tile_x) * (TILE_WIDTH * TILE_HEIGHT * bpp);

  // Z-order (Morton) swizzle for pixels within a tile
  uint32_t morton_offset = 0;
  uint32_t bit = 0;
  uint32_t m_x = in_tile_x;
  uint32_t m_y = in_tile_y;
  while (bit < (TILE_WIDTH * TILE_HEIGHT)) {
    morton_offset |= (m_x & 1) << bit++;
    m_x >>= 1;
    morton_offset |= (m_y & 1) << bit++;
    m_y >>= 1;
  }

  uint64_t in_tile_offset = morton_offset * bpp;

  return slice_offset + tile_offset_in_slice + in_tile_offset;
}

// Implementations for the new tiling modes
void TileManager::LinearToTiled2B(const TileInfo &info, const void *linearData,
                                  void *tiledData) const {
  // TILED_2B often has additional bank/pipe swizzling. For emulation,
  // we can treat it similarly to 2D but could add another XOR layer if needed.
  // For now, let's delegate to the standard 2D implementation.
  LinearToTiled2D(info, linearData, tiledData);
}

void TileManager::TiledToLinear2B(const TileInfo &info, const void *tiledData,
                                  void *linearData) const {
  TiledToLinear2D(info, tiledData, linearData);
}

void TileManager::LinearToTiled3D(const TileInfo &info, const void *linearData,
                                  void *tiledData) const {
  const auto *src_base = static_cast<const uint8_t *>(linearData);
  auto *dst_base = static_cast<uint8_t *>(tiledData);
  uint32_t linear_pitch = info.width * info.bytesPerPixel;
  uint32_t linear_slice_size = linear_pitch * info.height;

  for (uint32_t z = 0; z < info.depth; ++z) {
    for (uint32_t y = 0; y < info.height; ++y) {
      for (uint32_t x = 0; x < info.width; ++x) {
        uint64_t linear_offset = (uint64_t)z * linear_slice_size +
                                 (uint64_t)y * linear_pitch +
                                 x * info.bytesPerPixel;
        uint64_t tiled_offset = GetGcnTiledAddress(info, x, y, z);
        if (tiled_offset < info.slice * info.depth) {
          std::memcpy(dst_base + tiled_offset, src_base + linear_offset,
                      info.bytesPerPixel);
        }
      }
    }
  }
}

void TileManager::TiledToLinear3D(const TileInfo &info, const void *tiledData,
                                  void *linearData) const {
  const auto *src_base = static_cast<const uint8_t *>(tiledData);
  auto *dst_base = static_cast<uint8_t *>(linearData);
  uint32_t linear_pitch = info.width * info.bytesPerPixel;
  uint32_t linear_slice_size = linear_pitch * info.height;

  for (uint32_t z = 0; z < info.depth; ++z) {
    for (uint32_t y = 0; y < info.height; ++y) {
      for (uint32_t x = 0; x < info.width; ++x) {
        uint64_t tiled_offset = GetGcnTiledAddress(info, x, y, z);
        uint64_t linear_offset = (uint64_t)z * linear_slice_size +
                                 (uint64_t)y * linear_pitch +
                                 x * info.bytesPerPixel;
        if (tiled_offset < info.slice * info.depth) {
          std::memcpy(dst_base + linear_offset, src_base + tiled_offset,
                      info.bytesPerPixel);
        }
      }
    }
  }
}

void TileManager::LinearToTiledDepth(const TileInfo &info,
                                     const void *linearData,
                                     void *tiledData) const {
  // Depth tiling is a specialized 2D format. The main difference is often
  // metadata for compression (HTILE), which we are not emulating.
  // The address calculation can be considered the same as 2D for basic
  // emulation.
  LinearToTiled2D(info, linearData, tiledData);
}

void TileManager::TiledToLinearDepth(const TileInfo &info,
                                     const void *tiledData,
                                     void *linearData) const {
  TiledToLinear2D(info, tiledData, linearData);
}

// Existing conversion functions (can be simplified now)
void TileManager::LinearToTiled1D(const TileInfo &info, const void *linearData,
                                  void *tiledData) const {
  const auto *src = static_cast<const uint8_t *>(linearData);
  auto *dst = static_cast<uint8_t *>(tiledData);
  uint32_t linear_pitch = info.width * info.bytesPerPixel;
  for (uint32_t y = 0; y < info.height; ++y) {
    std::memcpy(dst + y * info.pitch, src + y * linear_pitch, linear_pitch);
  }
}

void TileManager::TiledToLinear1D(const TileInfo &info, const void *tiledData,
                                  void *linearData) const {
  const auto *src = static_cast<const uint8_t *>(tiledData);
  auto *dst = static_cast<uint8_t *>(linearData);
  uint32_t linear_pitch = info.width * info.bytesPerPixel;
  for (uint32_t y = 0; y < info.height; ++y) {
    std::memcpy(dst + y * linear_pitch, src + y * info.pitch, linear_pitch);
  }
}

void TileManager::LinearToTiled2D(const TileInfo &info, const void *linearData,
                                  void *tiledData) const {
  const auto *src_base = static_cast<const uint8_t *>(linearData);
  auto *dst_base = static_cast<uint8_t *>(tiledData);
  uint32_t linear_pitch = info.width * info.bytesPerPixel;

  for (uint32_t y = 0; y < info.height; ++y) {
    for (uint32_t x = 0; x < info.width; ++x) {
      uint64_t linear_offset =
          (uint64_t)y * linear_pitch + x * info.bytesPerPixel;
      uint64_t tiled_offset = GetGcnTiledAddress(info, x, y, 0);
      if (tiled_offset < info.slice) {
        std::memcpy(dst_base + tiled_offset, src_base + linear_offset,
                    info.bytesPerPixel);
      }
    }
  }
}

void TileManager::TiledToLinear2D(const TileInfo &info, const void *tiledData,
                                  void *linearData) const {
  const auto *src_base = static_cast<const uint8_t *>(tiledData);
  auto *dst_base = static_cast<uint8_t *>(linearData);
  uint32_t linear_pitch = info.width * info.bytesPerPixel;

  for (uint32_t y = 0; y < info.height; ++y) {
    for (uint32_t x = 0; x < info.width; ++x) {
      uint64_t tiled_offset = GetGcnTiledAddress(info, x, y, 0);
      uint64_t linear_offset =
          (uint64_t)y * linear_pitch + x * info.bytesPerPixel;
      if (tiled_offset < info.slice) {
        std::memcpy(dst_base + linear_offset, src_base + tiled_offset,
                    info.bytesPerPixel);
      }
    }
  }
}

// Other TileManager methods... (Clear, Copy, etc. remain the same)
void TileManager::ClearTiledSurface(uint64_t surfaceId, const float color[4]) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    auto it = m_surfaces.find(surfaceId);
    if (it == m_surfaces.end() || !it->second.active) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("ClearTiledSurface: Invalid surface ID: {}", surfaceId);
      throw TileManagerException("Invalid surface ID");
    }

    const TileInfo &info = it->second.info;
    std::vector<uint8_t> &data = it->second.data;
    lock.unlock();

    switch (info.format) {
    case TileFormat::R8_UNORM: {
      uint8_t value = static_cast<uint8_t>(color[0] * 255.0f);
      std::fill(data.begin(), data.end(), value);
      break;
    }
    case TileFormat::R8G8_UNORM: {
      uint8_t r = static_cast<uint8_t>(color[0] * 255.0f);
      uint8_t g = static_cast<uint8_t>(color[1] * 255.0f);
      uint16_t value = (static_cast<uint16_t>(g) << 8) | r;
      uint16_t *dataPtr = reinterpret_cast<uint16_t *>(data.data());
      std::fill(dataPtr, dataPtr + data.size() / 2, value);
      break;
    }
    case TileFormat::R8G8B8A8_UNORM: {
      uint8_t r = static_cast<uint8_t>(color[0] * 255.0f);
      uint8_t g = static_cast<uint8_t>(color[1] * 255.0f);
      uint8_t b = static_cast<uint8_t>(color[2] * 255.0f);
      uint8_t a = static_cast<uint8_t>(color[3] * 255.0f);
      uint32_t value = (static_cast<uint32_t>(a) << 24) |
                       (static_cast<uint32_t>(b) << 16) |
                       (static_cast<uint32_t>(g) << 8) | r;
      uint32_t *dataPtr = reinterpret_cast<uint32_t *>(data.data());
      std::fill(dataPtr, dataPtr + data.size() / 4, value);
      break;
    }
    case TileFormat::B8G8R8A8_UNORM: {
      uint8_t r = static_cast<uint8_t>(color[0] * 255.0f);
      uint8_t g = static_cast<uint8_t>(color[1] * 255.0f);
      uint8_t b = static_cast<uint8_t>(color[2] * 255.0f);
      uint8_t a = static_cast<uint8_t>(color[3] * 255.0f);
      uint32_t value = (static_cast<uint32_t>(a) << 24) |
                       (static_cast<uint32_t>(r) << 16) |
                       (static_cast<uint32_t>(g) << 8) | b;
      uint32_t *dataPtr = reinterpret_cast<uint32_t *>(data.data());
      std::fill(dataPtr, dataPtr + data.size() / 4, value);
      break;
    }
    case TileFormat::R32G32B32A32_FLOAT: {
      float *dataPtr = reinterpret_cast<float *>(data.data());
      for (size_t i = 0; i < data.size() / 16; ++i) {
        dataPtr[i * 4 + 0] = color[0];
        dataPtr[i * 4 + 1] = color[1];
        dataPtr[i * 4 + 2] = color[2];
        dataPtr[i * 4 + 3] = color[3];
      }
      break;
    }
    default:
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("ClearTiledSurface: Unsupported format: {}",
                    static_cast<int>(info.format));
      throw TileManagerException("Unsupported format for clear");
    }

    lock.lock();
    // Update cache
    SurfaceCacheEntry cacheEntry{surfaceId, data};
    m_surfaceCache[surfaceId] = cacheEntry;
    it->second.info.cacheHits++;
    it->second.cacheHits++;
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("ClearTiledSurface: Cleared surface {}, color=[{},{},{},{}], "
                 "latency={}us",
                 surfaceId, color[0], color[1], color[2], color[3], latency);
    if (info.isRenderTarget) {
      lock.unlock();
      m_gnmState.SetContextRegister(0xA001, 0xFFFFFFFF); // Example clear flag
      lock.lock();
    }

    // Notify CommandProcessor of surface clear for GPU synchronization
    try {
      // Pack RGBA clear color into uint32_t (A<<24 | B<<16 | G<<8 | R)
      uint32_t clearValue = (static_cast<uint32_t>(color[3] * 255.0f) << 24) |
                            (static_cast<uint32_t>(color[2] * 255.0f) << 16) |
                            (static_cast<uint32_t>(color[1] * 255.0f) << 8) |
                            static_cast<uint32_t>(color[0] * 255.0f);
      m_commandProcessor.NotifySurfaceClear(surfaceId, clearValue);
      spdlog::trace("Notified CommandProcessor of surface {} clear", surfaceId);
    } catch (const std::exception &e) {
      spdlog::warn("Failed to notify CommandProcessor of surface clear: {}",
                   e.what());
    }
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ClearTiledSurface failed: {}", e.what());
    throw TileManagerException("ClearTiledSurface failed: " +
                               std::string(e.what()));
  }
}

void TileManager::CopyTiledSurface(uint64_t dstSurfaceId,
                                   uint64_t srcSurfaceId) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    auto srcIt = m_surfaces.find(srcSurfaceId);
    auto dstIt = m_surfaces.find(dstSurfaceId);
    if (srcIt == m_surfaces.end() || !srcIt->second.active ||
        dstIt == m_surfaces.end() || !dstIt->second.active) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("CopyTiledSurface: Invalid surface IDs: src={}, dst={}",
                    srcSurfaceId, dstSurfaceId);
      throw TileManagerException("Invalid surface IDs");
    }

    const TileInfo &srcInfo = srcIt->second.info;
    TileInfo &dstInfo = dstIt->second.info;
    if (srcInfo.width != dstInfo.width || srcInfo.height != dstInfo.height ||
        srcInfo.depth != dstInfo.depth || srcInfo.format != dstInfo.format) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("CopyTiledSurface: Incompatible surfaces: src={}, dst={}",
                    srcSurfaceId, dstSurfaceId);
      throw TileManagerException("Incompatible surfaces");
    }

    dstIt->second.data = srcIt->second.data;

    // Update cache
    SurfaceCacheEntry cacheEntry{dstSurfaceId, dstIt->second.data};
    m_surfaceCache[dstSurfaceId] = cacheEntry;
    srcIt->second.info.cacheHits++;
    srcIt->second.cacheHits++;
    dstIt->second.info.cacheHits++;
    dstIt->second.cacheHits++;
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("CopyTiledSurface: Copied surface {} to {}, size={} bytes, "
                 "latency={}us",
                 srcSurfaceId, dstSurfaceId, srcIt->second.data.size(),
                 latency);

    lock.unlock();
    m_commandProcessor.NotifySurfaceCopy(srcSurfaceId, dstSurfaceId, 0, 0, 0, 0,
                                         srcInfo.width, srcInfo.height);
    lock.lock();

  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("CopyTiledSurface failed: {}", e.what());
    throw TileManagerException("CopyTiledSurface failed: " +
                               std::string(e.what()));
  }
}

void TileManager::GetCurrentTile(uint32_t &x, uint32_t &y, uint32_t &width,
                                 uint32_t &height) const {
  auto start = std::chrono::high_resolution_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    x = 0;
    y = 0;
    width = 1;
    height = 1;

    if (m_currentSurfaceId == 0) {
      m_stats.cacheHits++;
      spdlog::trace("GetCurrentTile: No active surface");
      return;
    }

    auto it = m_surfaces.find(m_currentSurfaceId);
    if (it == m_surfaces.end() || !it->second.active) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("GetCurrentTile: Invalid current surface ID: {}",
                    m_currentSurfaceId);
      return;
    }

    const TileInfo &info = it->second.info;
    width = std::min(TILE_WIDTH, info.width - m_currentTileX * TILE_WIDTH);
    height = std::min(TILE_HEIGHT, info.height - m_currentTileY * TILE_HEIGHT);
    x = m_currentTileX * TILE_WIDTH;
    y = m_currentTileY * TILE_HEIGHT;

    it->second.info.cacheHits++;
    it->second.cacheHits++;
    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetCurrentTile: surface={}, x={}, y={}, width={}, "
                  "height={}, latency={}us",
                  m_currentSurfaceId, x, y, width, height, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetCurrentTile failed: {}", e.what());
  }
}

void TileManager::NextTile() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    if (m_currentSurfaceId == 0 || m_tilesX == 0 || m_tilesY == 0) {
      m_stats.cacheHits++;
      spdlog::trace("NextTile: No active surface or tiles");
      return;
    }

    m_currentTileX++;
    if (m_currentTileX >= m_tilesX) {
      m_currentTileX = 0;
      m_currentTileY++;
      if (m_currentTileY >= m_tilesY) {
        m_currentTileY = 0;
      }
    }

    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("NextTile: surface={}, tileX={}, tileY={}, latency={}us",
                  m_currentSurfaceId, m_currentTileX, m_currentTileY, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("NextTile failed: {}", e.what());
  }
}

size_t TileManager::GetTotalTiles() const {
  auto start = std::chrono::high_resolution_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    size_t totalTiles = 0;
    for (const auto &[id, surface] : m_surfaces) {
      if (surface.active) {
        totalTiles += (surface.info.width / TILE_WIDTH) *
                      (surface.info.height / TILE_HEIGHT);
      }
    }

    m_stats.operationCount++;
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetTotalTiles: total={}, latency={}us", totalTiles, latency);
    return totalTiles;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetTotalTiles failed: {}", e.what());
    return 0;
  }
}

bool TileManager::GetCachedSurfaceData(uint64_t surfaceId,
                                       std::vector<uint8_t> &data) const {
  auto start = std::chrono::high_resolution_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    auto it = m_surfaceCache.find(surfaceId);
    if (it != m_surfaceCache.end()) {
      data = it->second.data;
      it->second.cacheHits++;
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::trace(
          "GetCachedSurfaceData: surface={}, hit, size={} bytes, latency={}us",
          surfaceId, data.size(), latency);
      return true;
    }
    m_stats.cacheMisses++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetCachedSurfaceData: surface={}, miss, latency={}us",
                  surfaceId, latency);
    return false;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetCachedSurfaceData failed: {}", e.what());
    return false;
  }
}

void TileManager::ClearSurfaceCache() {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    m_surfaceCache.clear();
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("ClearSurfaceCache: Cleared cache, latency={}us", latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("ClearSurfaceCache failed: {}", e.what());
  }
}

TileManagerStats TileManager::GetStats() const {
  auto start = std::chrono::high_resolution_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::trace("GetStats: latency={}us", latency);
    return m_stats;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("GetStats failed: {}", e.what());
    return m_stats;
  }
}

void TileManager::SaveState(std::ostream &out) const {
  auto start = std::chrono::high_resolution_clock::now();
  std::shared_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    uint32_t version = 1; // Serialization version
    out.write(reinterpret_cast<const char *>(&version), sizeof(version));

    uint64_t surfaceCount = m_surfaces.size();
    out.write(reinterpret_cast<const char *>(&surfaceCount),
              sizeof(surfaceCount));
    for (const auto &[id, surface] : m_surfaces) {
      out.write(reinterpret_cast<const char *>(&id), sizeof(id));
      surface.Serialize(out);
    }

    uint64_t cacheCount = m_surfaceCache.size();
    out.write(reinterpret_cast<const char *>(&cacheCount), sizeof(cacheCount));
    for (const auto &[id, entry] : m_surfaceCache) {
      out.write(reinterpret_cast<const char *>(&id), sizeof(id));
      uint64_t dataSize = entry.data.size();
      out.write(reinterpret_cast<const char *>(&dataSize), sizeof(dataSize));
      out.write(reinterpret_cast<const char *>(entry.data.data()), dataSize);
      out.write(reinterpret_cast<const char *>(&entry.cacheHits),
                sizeof(entry.cacheHits));
      out.write(reinterpret_cast<const char *>(&entry.cacheMisses),
                sizeof(entry.cacheMisses));
    }

    out.write(reinterpret_cast<const char *>(&m_nextSurfaceId),
              sizeof(m_nextSurfaceId));
    out.write(reinterpret_cast<const char *>(&m_currentSurfaceId),
              sizeof(m_currentSurfaceId));
    out.write(reinterpret_cast<const char *>(&m_currentTileX),
              sizeof(m_currentTileX));
    out.write(reinterpret_cast<const char *>(&m_currentTileY),
              sizeof(m_currentTileY));
    out.write(reinterpret_cast<const char *>(&m_tilesX), sizeof(m_tilesX));
    out.write(reinterpret_cast<const char *>(&m_tilesY), sizeof(m_tilesY));
    out.write(reinterpret_cast<const char *>(&m_stats), sizeof(m_stats));

    if (!out.good()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw TileManagerException("Failed to write tile manager state");
    }

    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("SaveState: Saved tile manager state, surfaces={}, "
                 "cache_count={}, latency={}us",
                 surfaceCount, cacheCount, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SaveState failed: {}", e.what());
    throw TileManagerException("SaveState failed: " + std::string(e.what()));
  }
}

void TileManager::LoadState(std::istream &in) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    uint32_t version;
    in.read(reinterpret_cast<char *>(&version), sizeof(version));
    if (version != 1) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw TileManagerException("Unsupported tile manager state version: " +
                                 std::to_string(version));
    }

    m_surfaces.clear();
    uint64_t surfaceCount;
    in.read(reinterpret_cast<char *>(&surfaceCount), sizeof(surfaceCount));
    for (uint64_t i = 0; i < surfaceCount && in.good(); ++i) {
      uint64_t id;
      TiledSurface surface;
      in.read(reinterpret_cast<char *>(&id), sizeof(id));
      surface.Deserialize(in);
      m_surfaces[id] = surface;
    }

    m_surfaceCache.clear();
    uint64_t cacheCount;
    in.read(reinterpret_cast<char *>(&cacheCount), sizeof(cacheCount));
    for (uint64_t i = 0; i < cacheCount && in.good(); ++i) {
      uint64_t id;
      SurfaceCacheEntry entry;
      in.read(reinterpret_cast<char *>(&id), sizeof(id));
      uint64_t dataSize;
      in.read(reinterpret_cast<char *>(&dataSize), sizeof(dataSize));
      entry.data.resize(dataSize);
      in.read(reinterpret_cast<char *>(entry.data.data()), dataSize);
      in.read(reinterpret_cast<char *>(&entry.cacheHits),
              sizeof(entry.cacheHits));
      in.read(reinterpret_cast<char *>(&entry.cacheMisses),
              sizeof(entry.cacheMisses));
      entry.surfaceId = id;
      m_surfaceCache[id] = entry;
    }

    in.read(reinterpret_cast<char *>(&m_nextSurfaceId),
            sizeof(m_nextSurfaceId));
    in.read(reinterpret_cast<char *>(&m_currentSurfaceId),
            sizeof(m_currentSurfaceId));
    in.read(reinterpret_cast<char *>(&m_currentTileX), sizeof(m_currentTileX));
    in.read(reinterpret_cast<char *>(&m_currentTileY), sizeof(m_currentTileY));
    in.read(reinterpret_cast<char *>(&m_tilesX), sizeof(m_tilesX));
    in.read(reinterpret_cast<char *>(&m_tilesY), sizeof(m_tilesY));
    in.read(reinterpret_cast<char *>(&m_stats), sizeof(m_stats));

    if (!in.good()) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      throw TileManagerException("Failed to read tile manager state");
    }

    m_stats.cacheHits++;
    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;
    spdlog::info("LoadState: Loaded tile manager state, surfaces={}, "
                 "cache_count={}, latency={}us",
                 surfaceCount, cacheCount, latency);
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("LoadState failed: {}", e.what());
    throw TileManagerException("LoadState failed: " + std::string(e.what()));
  }
}

bool TileManager::SetTileMode(uint64_t surfaceId, uint32_t tileMode) {
  auto start = std::chrono::high_resolution_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_surfaceMutex);
  try {
    auto it = m_surfaces.find(surfaceId);
    if (it == m_surfaces.end() || !it->second.active) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("SetTileMode: Invalid surface ID: {}", surfaceId);
      throw TileManagerException("Invalid surface ID");
    }

    // Validate tile mode
    if (tileMode > static_cast<uint32_t>(TileMode::TILED_DEPTH)) {
      m_stats.errorCount++;
      m_stats.cacheMisses++;
      spdlog::error("SetTileMode: Invalid tile mode: {}", tileMode);
      throw TileManagerException("Invalid tile mode");
    }

    TileInfo &info = it->second.info;
    TileMode oldMode = info.mode;
    TileMode newMode = static_cast<TileMode>(tileMode);

    // If mode hasn't changed, just return success
    if (oldMode == newMode) {
      m_stats.cacheHits++;
      auto end = std::chrono::high_resolution_clock::now();
      auto latency =
          std::chrono::duration_cast<std::chrono::microseconds>(end - start)
              .count();
      m_stats.totalLatencyUs += latency;
      spdlog::info(
          "SetTileMode: Mode already set for surface {}, mode={}, latency={}us",
          surfaceId, tileMode, latency);
      return true;
    }

    // Create a copy of the current data in linear format
    std::vector<uint8_t> linearData(info.width * info.height * info.depth *
                                    info.bytesPerPixel);
    TiledToLinear(surfaceId, linearData.data(), linearData.size());

    // Update tile mode
    info.mode = newMode;

    // Recalculate layout with new mode
    CalculateTileLayout(info);

    // Resize data buffer if needed
    size_t newSize = info.slice * info.depth;
    if (it->second.data.size() != newSize) {
      it->second.data.resize(newSize, 0);
    }

    // Convert linear data back to the new tile format
    LinearToTiled(surfaceId, linearData.data(), linearData.size());

    // Update cache
    SurfaceCacheEntry cacheEntry{surfaceId, it->second.data};
    m_surfaceCache[surfaceId] = cacheEntry;

    info.cacheHits++;
    it->second.cacheHits++;
    m_stats.operationCount++;
    m_stats.cacheHits++;

    auto end = std::chrono::high_resolution_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;

    spdlog::info(
        "SetTileMode: Updated surface {} tile mode from {} to {}, latency={}us",
        surfaceId, static_cast<int>(oldMode), tileMode, latency);

    lock.unlock();
    m_commandProcessor.NotifySurfaceUpdated(surfaceId, 0, 0, info.width,
                                            info.height);

    return true;
  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("SetTileMode failed: {}", e.what());
    throw TileManagerException("SetTileMode failed: " + std::string(e.what()));
  }
}

uint64_t TileManager::MapTiledMemory(uint64_t cpuAddress, size_t size,
                                     uint32_t tileMode) {
  auto start = std::chrono::steady_clock::now();
  std::unique_lock<std::shared_mutex> lock(m_mutex);

  try {
    spdlog::info("Mapping tiled memory: cpu=0x{:x}, size={}, tileMode={}",
                 cpuAddress, size, tileMode);

    // Validate input parameters
    if (size == 0) {
      throw TileManagerException(
          "Invalid size (zero) for tiled memory mapping");
    }

    if (tileMode > static_cast<uint32_t>(TileMode::TILED_DEPTH)) {
      throw TileManagerException("Invalid tile mode for memory mapping");
    }

    // Create a tiled surface using the CPU address as an ID
    // This ensures we can find it later when needed
    uint64_t surfaceId = cpuAddress;

    // Calculate dimensions based on size
    // For simplicity, we'll use a square layout that can hold the data
    uint32_t bytesPerPixel = 4; // Assume RGBA by default
    uint32_t pixelCount =
        static_cast<uint32_t>((size + bytesPerPixel - 1) / bytesPerPixel);
    uint32_t dimension =
        static_cast<uint32_t>(std::ceil(std::sqrt(pixelCount)));

    // Round up to tile boundaries
    dimension = ((dimension + TILE_WIDTH - 1) / TILE_WIDTH) * TILE_WIDTH;

    // Create the surface with appropriate dimensions
    TiledSurface surface;
    surface.info.width = dimension;
    surface.info.height = dimension;
    surface.info.depth = 1;
    surface.info.mode = static_cast<TileMode>(tileMode);
    surface.info.format = TileFormat::R8G8B8A8_UNORM; // Default format
    surface.info.bytesPerPixel = bytesPerPixel;
    surface.info.gpuAddr = surfaceId;
    surface.info.cpuAddr = cpuAddress;
    surface.info.isRenderTarget = false;
    surface.info.isDepthStencil = false;

    // Calculate layout based on the tile mode
    CalculateTileLayout(surface.info);

    // Allocate memory for the surface
    size_t totalSize = surface.info.slice * surface.info.depth;
    surface.data.resize(totalSize, 0);
    surface.active = true;

    // Store the surface
    m_surfaces[surfaceId] = std::move(surface);

    // Cache the surface data
    SurfaceCacheEntry cacheEntry{surfaceId, m_surfaces[surfaceId].data};
    m_surfaceCache[surfaceId] = cacheEntry;

    m_stats.operationCount++;
    m_stats.cacheHits++;

    auto end = std::chrono::steady_clock::now();
    auto latency =
        std::chrono::duration_cast<std::chrono::microseconds>(end - start)
            .count();
    m_stats.totalLatencyUs += latency;

    spdlog::info("Tiled memory mapped: cpu=0x{:x} -> gpu=0x{:x}, size={}, "
                 "tileMode={}, latency={}us",
                 cpuAddress, surfaceId, size, tileMode, latency);

    // Return the GPU address (which is the same as the surface ID in this
    // implementation)
    return surfaceId;

  } catch (const std::exception &e) {
    m_stats.errorCount++;
    m_stats.cacheMisses++;
    spdlog::error("MapTiledMemory failed: {}", e.what());
    throw TileManagerException("MapTiledMemory failed: " +
                               std::string(e.what()));
  }
}

} // namespace ps4